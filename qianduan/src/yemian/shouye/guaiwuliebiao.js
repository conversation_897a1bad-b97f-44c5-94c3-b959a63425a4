import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { meiti_chaxun, useShebeileixingJiance } from '../../gongju/shebeishiPei_gongju.js';
import { wangguanpeizhi } from '../../peizhi/wangguanpeizhi.js';

// 怪物列表容器
const Guaiwuliebiaorongqi = styled.div`
  width: 100%;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}18, ${props.theme.yanse.danjinse_qian}12, ${props.theme.yanse.danjinse}08)`
    : `linear-gradient(145deg, ${props.theme.yanse.biaomian}, rgba(255, 255, 255, 0.95))`};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}50`
    : `${props.theme.yanse.biankuang}80`};
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 8px 32px ${props.theme.yanse.danjinse}25, 0 4px 16px rgba(0, 0, 0, 0.3), inset 0 1px 0 ${props.theme.yanse.danjinse}30`
    : `0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8)`};
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}60, transparent)`
      : `linear-gradient(90deg, transparent, ${props.theme.yanse.zhuyao}40, transparent)`};
  }

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    border-radius: 16px;
    margin-bottom: 18px;
    margin-left: 0;
    margin-right: 0;
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 6px 24px ${props.theme.yanse.danjinse}20, 0 3px 12px rgba(0, 0, 0, 0.25)`
      : `0 6px 24px rgba(0, 0, 0, 0.1), 0 3px 12px rgba(0, 0, 0, 0.06)`};
  }

  ${meiti_chaxun.pingban} {
    border-radius: 18px;
    margin-bottom: 20px;
    max-width: 100%;
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 7px 28px ${props.theme.yanse.danjinse}22, 0 3px 14px rgba(0, 0, 0, 0.28)`
      : `0 7px 28px rgba(0, 0, 0, 0.11), 0 3px 14px rgba(0, 0, 0, 0.07)`};
  }

  /* 桌面端确保容器不超出边界 */
  ${meiti_chaxun.zhuomian} {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
`;

// 标题区域
const Biaotiquyu = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}35`
    : `${props.theme.yanse.biankuang}60`};
  display: flex;
  align-items: center;
  gap: 16px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}12, ${props.theme.yanse.danjinse_qian}08)`
    : `linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7))`};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(90deg, transparent, ${props.theme.yanse.danjinse}50, transparent)`
      : `linear-gradient(90deg, transparent, ${props.theme.yanse.zhuyao}30, transparent)`};
  }

  ${meiti_chaxun.shouji} {
    padding: 16px 20px;
    gap: 12px;

    &::after {
      left: 20px;
      right: 20px;
    }
  }

  ${meiti_chaxun.pingban} {
    padding: 18px 22px;
    gap: 14px;

    &::after {
      left: 22px;
      right: 22px;
    }
  }
`;

// 怪物数据图标
const Guaiwutubiao = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70, ${props.theme.yanse.danjinse}50)`
    : `linear-gradient(135deg, ${props.theme.yanse.zhuyao}90, ${props.theme.yanse.zhuyao}70, ${props.theme.yanse.zhuyao}50)`};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  flex-shrink: 0;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}40, inset 0 1px 0 rgba(255, 255, 255, 0.2)`
    : `0 4px 12px ${props.theme.yanse.zhuyao}30, inset 0 1px 0 rgba(255, 255, 255, 0.3)`};
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 10px;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}20, transparent)`
      : `linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent)`};
    pointer-events: none;
  }

  ${meiti_chaxun.shouji} {
    width: 32px;
    height: 32px;
    font-size: 16px;
    border-radius: 8px;

    &::before {
      border-radius: 6px;
    }
  }

  ${meiti_chaxun.pingban} {
    width: 36px;
    height: 36px;
    font-size: 17px;
    border-radius: 10px;

    &::before {
      border-radius: 8px;
    }
  }
`;

// 标题文字
const Biaotiwenzi = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  flex: 1;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}90, ${props.theme.yanse.danjinse_qian}70)`
    : `linear-gradient(135deg, ${props.theme.yanse.zhuyao}90, ${props.theme.yanse.zhuyao}70)`};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 2px 4px ${props.theme.yanse.danjinse}30`
    : `0 2px 4px ${props.theme.yanse.zhuyao}20`};

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }
`;

// 怪物列表滚动容器
const Guaiwugundongrongqi = styled.div`
  padding: 16px 20px 16px 0; /* 去掉左边的padding */
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  max-width: 100%; /* 使用父容器的宽度限制 */

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  ${meiti_chaxun.shouji} {
    padding: 10px 0; /* 完全去掉左右padding */
    max-width: 100%;
  }

  ${meiti_chaxun.pingban} {
    padding: 14px 18px 14px 0; /* 去掉左边的padding */
    max-width: 100%;
  }

  /* 桌面端确保不超出父容器 */
  ${meiti_chaxun.zhuomian} {
    max-width: 100%;
    padding-right: 20px; /* 保留右边距，避免贴边 */
  }
`;

// 怪物列表容器
const Guaiwuliebiaoliebiao = styled.div`
  display: flex;
  gap: 16px;
  min-width: fit-content;
  margin-left: 0; /* 确保没有左边距 */
  padding-left: 0; /* 彻底去掉左边的内边距 */

  ${meiti_chaxun.shouji} {
    gap: 10px;
    padding-left: 0;
  }

  ${meiti_chaxun.pingban} {
    gap: 14px;
    padding-left: 0;
  }
`;

// 怪物卡片
const Guaiwukapian = styled(motion.div)`
  min-width: 220px;
  width: 220px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(145deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}08, ${props.theme.yanse.danjinse}12)`
    : `linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85), rgba(255, 255, 255, 0.9))`};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : `${props.theme.yanse.biankuang}70`};
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 20px ${props.theme.yanse.danjinse}20, 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 ${props.theme.yanse.danjinse}25`
    : `0 4px 20px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.6)`};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}05, transparent, ${props.theme.yanse.danjinse_qian}03)`
      : `linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.1))`};
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 12px 40px ${props.theme.yanse.danjinse}35, 0 6px 20px rgba(0, 0, 0, 0.4), inset 0 1px 0 ${props.theme.yanse.danjinse}40`
      : `0 12px 40px rgba(0, 0, 0, 0.15), 0 6px 20px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8)`};
    border-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}70`
      : `${props.theme.yanse.zhuyao}60`};

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-3px) scale(1.01);
    transition: all 0.15s ease;
  }

  ${meiti_chaxun.shouji} {
    min-width: 140px;
    width: 140px;
    padding: 12px;
    border-radius: 12px;

    &:hover {
      transform: translateY(-3px) scale(1.01);
    }

    &:active {
      transform: translateY(-1px) scale(1.005);
    }
  }

  ${meiti_chaxun.pingban} {
    min-width: 180px;
    width: 180px;
    padding: 16px;
    border-radius: 14px;

    &:hover {
      transform: translateY(-4px) scale(1.015);
    }

    &:active {
      transform: translateY(-2px) scale(1.01);
    }
  }
`;

// 怪物头像
const Guaiwutouxiang = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}20, ${props.theme.yanse.danjinse_qian}10)`
    : `linear-gradient(135deg, ${props.theme.yanse.zhuyao}20, ${props.theme.yanse.zhuyao}10)`};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0 auto 12px auto;
  border: 2px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};

  ${meiti_chaxun.shouji} {
    width: 35px;
    height: 35px;
    font-size: 16px;
    margin-bottom: 6px;
    border-radius: 8px;
    border-width: 1px;
  }

  ${meiti_chaxun.pingban} {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-bottom: 10px;
    border-radius: 9px;
  }
`;

// 怪物名称
const Guaiwumingcheng = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-align: center;
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    margin-bottom: 5px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    margin-bottom: 7px;
  }
`;

// 怪物等级
const Guaiwudengji = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  text-align: center;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}20`
    : props.theme.yanse.beijing_qian};
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};

  ${meiti_chaxun.shouji} {
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    padding: 3px 7px;
    border-radius: 5px;
  }
`;

// 加载状态
const Jiazaizhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 错误状态
const Cuowuzhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.cuowu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 怪物列表组件
function Guaiwuliebiao() {
  const [guaiwushuju, shezhi_guaiwushuju] = useState([]);
  const [jiazaizhong, shezhi_jiazaizhong] = useState(true);
  const [cuowu, shezhi_cuowu] = useState(null);

  // 滚动相关状态和引用
  const gundongrongqiref = useRef(null);
  const [zhengzaigundong, shezhi_zhengzaigundong] = useState(false);
  const [shubiaoruyu, shezhi_shubiaoruyu] = useState(false);
  const gundongdonghuaref = useRef(null);

  // 设备检测
  const { shuchu_tiaoshi_xinxi } = useShebeileixingJiance();

  // 模拟怪物数据（临时使用）
  const moniGuaiwushuju = [
    { guaiwu_id: 1001, guaiwu_mingcheng: '波利', level: 1, yuansu: ['无'], zhongzu: ['植物'] },
    { guaiwu_id: 1002, guaiwu_mingcheng: '小强', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1003, guaiwu_mingcheng: '蘑菇宝贝', level: 3, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1004, guaiwu_mingcheng: '红蝙蝠', level: 5, yuansu: ['暗'], zhongzu: ['动物'] },
    { guaiwu_id: 1005, guaiwu_mingcheng: '绿水母', level: 4, yuansu: ['水'], zhongzu: ['鱼贝'] },
    { guaiwu_id: 1006, guaiwu_mingcheng: '小鸡', level: 1, yuansu: ['无'], zhongzu: ['动物'] },
    { guaiwu_id: 1007, guaiwu_mingcheng: '蚯蚓', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1008, guaiwu_mingcheng: '树精', level: 6, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1009, guaiwu_mingcheng: '火狐', level: 8, yuansu: ['火'], zhongzu: ['动物'] },
    { guaiwu_id: 1010, guaiwu_mingcheng: '冰晶', level: 7, yuansu: ['水'], zhongzu: ['无机物'] },
    { guaiwu_id: 1011, guaiwu_mingcheng: '雷鸟', level: 10, yuansu: ['风'], zhongzu: ['动物'] },
    { guaiwu_id: 1012, guaiwu_mingcheng: '石巨人', level: 12, yuansu: ['地'], zhongzu: ['无机物'] },
    { guaiwu_id: 1013, guaiwu_mingcheng: '暗影', level: 15, yuansu: ['暗'], zhongzu: ['恶魔'] },
    { guaiwu_id: 1014, guaiwu_mingcheng: '光精灵', level: 14, yuansu: ['圣'], zhongzu: ['精灵'] },
    { guaiwu_id: 1015, guaiwu_mingcheng: '毒蛇', level: 9, yuansu: ['毒'], zhongzu: ['动物'] },
    { guaiwu_id: 1016, guaiwu_mingcheng: '钢铁兽', level: 18, yuansu: ['无'], zhongzu: ['机械'] }
  ];

  // 获取怪物头像表情符号
  const huoquguaiwubiaoqing = (mingcheng) => {
    const biaoqingying = {
      '波利': '🟢',
      '小强': '🪲',
      '蘑菇宝贝': '🍄',
      '红蝙蝠': '🦇',
      '绿水母': '🪼',
      '小鸡': '🐣',
      '蚯蚓': '🪱',
      '树精': '🌳',
      '火狐': '🦊',
      '冰晶': '❄️',
      '雷鸟': '⚡',
      '石巨人': '🗿',
      '暗影': '👤',
      '光精灵': '✨',
      '毒蛇': '🐍',
      '钢铁兽': '🤖'
    };
    return biaoqingying[mingcheng] || '👾';
  };

  // 检查是否支持鼠标悬浮滚动
  const zhichishubiaogundongg = () => {
    // 检测是否支持hover和精确指针
    const zhichi_hover = window.matchMedia('(hover: hover)').matches;
    const zhichi_pointer = window.matchMedia('(pointer: fine)').matches;
    return zhichi_hover && zhichi_pointer;
  };

  // 平滑滚动函数
  const pinghuagundong = (mubiaoweizhii, shijian = 300) => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi) return;

    const kaishiweizhii = rongqi.scrollLeft;
    const jianli = mubiaoweizhii - kaishiweizhii;
    const kaishishijian = performance.now();

    const donghua = (dangqianshijian) => {
      const jindu = Math.min((dangqianshijian - kaishishijian) / shijian, 1);
      const huanmancurve = 1 - Math.pow(1 - jindu, 3); // easeOut cubic

      rongqi.scrollLeft = kaishiweizhii + jianli * huanmancurve;

      if (jindu < 1) {
        gundongdonghuaref.current = requestAnimationFrame(donghua);
      } else {
        shezhi_zhengzaigundong(false);
      }
    };

    if (gundongdonghuaref.current) {
      cancelAnimationFrame(gundongdonghuaref.current);
    }

    shezhi_zhengzaigundong(true);
    gundongdonghuaref.current = requestAnimationFrame(donghua);
  };



  // 处理鼠标进入事件
  const chulishubiaoruyu = () => {
    const zhichi = zhichishubiaogundongg();

    if (zhichi) {
      shezhi_shubiaoruyu(true);
    }
  };

  // 处理鼠标离开事件
  const chulishubiaolikai = () => {
    shezhi_shubiaoruyu(false);
    if (gundongdonghuaref.current) {
      cancelAnimationFrame(gundongdonghuaref.current);
      shezhi_zhengzaigundong(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    const jiazaiguaiwushuju = async () => {
      try {
        shezhi_jiazaizhong(true);
        shezhi_cuowu(null);
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 暂时使用模拟数据
        shezhi_guaiwushuju(moniGuaiwushuju);
        
      } catch (error) {
        console.error('加载怪物数据失败:', error);
        shezhi_cuowu('加载怪物数据失败，请稍后重试');
      } finally {
        shezhi_jiazaizhong(false);
      }
    };

    jiazaiguaiwushuju();
  }, []);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      if (gundongdonghuaref.current) {
        cancelAnimationFrame(gundongdonghuaref.current);
      }
    };
  }, []);

  // 添加原生滚轮事件监听器
  useEffect(() => {
    const rongqi = gundongrongqiref.current;
    if (!rongqi) return;

    const chuliyuanshenggunlun = (event) => {
      const zhichi = zhichishubiaogundongg();

      if (!zhichi || !shubiaoruyu) return;

      const gundonglianggg = event.deltaY * 2;
      const dangqianweizhii = rongqi.scrollLeft;
      const zuida_gundong = rongqi.scrollWidth - rongqi.clientWidth;
      const xinweizhii = Math.max(0, Math.min(zuida_gundong, dangqianweizhii + gundonglianggg));

      if (zuida_gundong > 0) {
        event.preventDefault();
        pinghuagundong(xinweizhii, 150);
      }
    };

    rongqi.addEventListener('wheel', chuliyuanshenggunlun, { passive: false });

    return () => {
      rongqi.removeEventListener('wheel', chuliyuanshenggunlun);
    };
  }, [shubiaoruyu]);

  // 处理怪物卡片点击
  const chulidianjikapian = (guaiwu) => {
    console.log('点击怪物:', guaiwu);
    // 这里可以添加跳转到怪物详情页的逻辑
  };

  return (
    <Guaiwuliebiaorongqi>
      {/* 标题区域 */}
      <Biaotiquyu>
        <Guaiwutubiao>👾</Guaiwutubiao>
        <Biaotiwenzi>怪物数据</Biaotiwenzi>
      </Biaotiquyu>

      {/* 内容区域 */}
      {jiazaizhong ? (
        <Jiazaizhuangtai>正在加载怪物数据...</Jiazaizhuangtai>
      ) : cuowu ? (
        <Cuowuzhuangtai>{cuowu}</Cuowuzhuangtai>
      ) : (
        <Guaiwugundongrongqi
          ref={gundongrongqiref}
          onMouseEnter={chulishubiaoruyu}
          onMouseLeave={chulishubiaolikai}
        >
          <Guaiwuliebiaoliebiao>
            <AnimatePresence>
              {guaiwushuju.map((guaiwu, suoyin) => (
                <Guaiwukapian
                  key={guaiwu.guaiwu_id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3, delay: suoyin * 0.1 }}
                  onClick={() => chulidianjikapian(guaiwu)}
                >
                  <Guaiwutouxiang>
                    {huoquguaiwubiaoqing(guaiwu.guaiwu_mingcheng)}
                  </Guaiwutouxiang>
                  <Guaiwumingcheng>{guaiwu.guaiwu_mingcheng}</Guaiwumingcheng>
                  <Guaiwudengji>Lv.{guaiwu.level}</Guaiwudengji>
                </Guaiwukapian>
              ))}
            </AnimatePresence>
          </Guaiwuliebiaoliebiao>
        </Guaiwugundongrongqi>
      )}
    </Guaiwuliebiaorongqi>
  );
}

export default Guaiwuliebiao;
